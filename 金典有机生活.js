/**
 * 小程序：金典有机生活
 * export jdck='wxid#备注'
 * export PHONECODE_SERVER='http://your-wechat-server-url' // 获取微信code的服务地址
 */
const Env = require('./env');
const $ = new Env('金典有机生活')
const CKS = process.env.jdck || '';
const PHONECODE_SERVER = process.env.PHONECODE_SERVER || ''; // 获取微信code的服务地址,可以"这里填也可以环境变量填
let sfbf = 0;//是否并发  1是 0否
let appid = 'wxf32616183fb4511e'
let notice = ''
let wxidArr = {}
let accesstokenArr = {}
let tokenArr = {}
let headArr = {}
let goods_notice = ''
let is_goods = true;
let ua = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13)XWEB/11581'
let end = []
!(async () => {
    await main();
})().catch((e) => {
    $.log(e)
}).finally(() => {
    $.done({});
});

async function main() {
    if (!CKS) {
        console.log("未填写ck")
        return
    }
    if (!PHONECODE_SERVER) {
        console.log("未配置PHONECODE_SERVER环境变量，无法获取登录code")
        return
    }
    let arr = Array.isArray(CKS) ? CKS : CKS.split("\n");
    for (const num in arr) {
        //await $.wait($.randomInt(60000, 180000))
        let item = arr[num];
        wxidArr[num] = item.split("#")[0]
        let remark = item.split('#')[1] || Number(num) + 1
        headArr[num] = `【账号 [${remark}] 】`
        if (sfbf) {
            begin(num)
        } else {
            await begin(num)
        }
    }
    while (true) {
        await $.wait(2000)
        if (end.length == arr.length) {
            break;
        }
    }
    console.log(goods_notice)
    notice += goods_notice;
    if (notice) {
        await sendMsg(notice);
    }
}

async function begin(num) {

    let key = `jdyjsh-${wxidArr[num]}`
    accesstokenArr[num] = $.getdata(key) || ''
    let isLogin = true
    if (accesstokenArr[num]) {
        let check = await commonGet(num, `/gateway/api/auth/account/user/info`)
        if (!check.status) {
            accesstokenArr[num] = '';
            print(num, check.error.msg);
        } else {
            isLogin = false;
            print(num, `缓存登录成功`)
        }
    }

    if (isLogin) {
        let wxcode = await getWxCode(num)
        if (wxcode.Code == 0) {
        } else {
            print(num, `获取微信code失败:${wxcode.Message}`, true)
            end.push(num)
            return
        }

        let login = await commonPost(num, `/gateway/api/auth/account/login`, {"jsCode": wxcode.Data.code})
        if (login.status) {
            accesstokenArr[num] = login.data.accessToken
            $.setdata(accesstokenArr[num], key)
            print(num, `登录成功`)
        } else {
            print(num, `登录失败:${login.error.msg}`)
            end.push(num)
            return;
        }
    }

    let signType = await commonGet(num, `/gateway/api/member/sign/status`)
    if (signType.status) {
        if (!signType.data.signed) {
            let signin = await commonPost(num, `/gateway/api/member/daily/sign`)
            if (signin.status) {
                // console.log(signin)
                print(num, `签到成功,获得积分:${signin.data.dailySign.bonusPoint}`)
            } else {
                print(num, `签到失败:${signin.error.msg}`)
            }
        } else {
            print(num, `今日已签到`)
        }
    } else {
        print(num, `查询签到失败:${signType.error.msg}`)
    }

    let point = await commonGet(num, `/gateway/api/member/point`)
    if (point.status) {
        print(num, `当前积分:${point.data}`, true)
    } else {
        print(num, `查询积分失败:${point.error.msg}`)
    }

    await activity(num)

    if (is_goods) {
        is_goods = false;
        let now = new Date().getTime()
        let goodsList = await commonPost(num, `/gateway/api/point/market/product/list`, {})
        if (goodsList.status) {
            for (let goods of goodsList.data) {
                if (goods.stdPrice == 0 && now > goods.exchangeStartTime && now < goods.exchangeEndTime) {
                    goods_notice += `【${goods.title}】积分[${goods.stdPoints}]\n`
                }
            }
        } else {
            print(num, `查询商品失败:${goodsList.error.msg}`)
        }
    }

    end.push(num)
}

async function activity(num) {
    let auth = await commonGet(num, `/developer/oauth2/buyer/authorize?app_key=zd123a10187c995e97`)
    let login = await acPost(num, `/wx-camp-jdxlh/stage/userLogin`, {"code":auth.data,"byOpenId":""})
    if (login.code == 1) {
        tokenArr[num] = login.data.token;
        let taskList = await acPost(num, `/wx-camp-jdxlh/stage/task/list`, {"pageType":1})
        if (taskList.code == 1) {
            for (let task of taskList.data) {
                if (task.name == '分享任务' && task.taskStatus == 0) {
                    let share = await acPost(num, `/wx-camp-jdxlh/stage/shareTask`, {})
                    if (share.code == 1) {
                        print(num, `分享成功`)
                    } else {
                        print(num, `分享失败,${share.msg}`)
                    }
                }
            }
        }
        let lotCnt = await acPost(num, `/wx-camp-jdxlh/stage/qryUserInfo`, {})
        if (lotCnt.code == 1) {
            let point = lotCnt.data.memberPoints
            for (let i = 0; i < lotCnt.data.lotteryNum; i++) {
                if (point > 10) {
                    let lot = await acPost(num, `/wx-camp-jdxlh/stage/lottery`, {})
                    if (lot.code == 1) {
                        print(num, `抽奖获得:${lot.data.awardName}`, true)
                    } else {
                        print(num, `抽奖失败,${share.msg}`)
                    }
                    point -= 10;
                }
            }
        } else {
            print(num, `查询抽奖次数失败,${lotCnt.msg}`)
        }
    } else {
        print(num, `活动登录失败`)
    }
}

async function commonGet(num, url, count = 5) {
    if (count <= 0) {
        throw new Error('请求重试次数超限');
    }
    return new Promise((resolve, reject) => {
        const options = {
            url: `https://msmarket.msx.digitalyili.com${url}`,
            headers: {
                "host": "msmarket.msx.digitalyili.com",
                "register-source": "",
                "access-token": accesstokenArr[num],
                "forward-appid": "",
                "source-type": "",
                "content-type": "application/json",
                "atv-page": "",
                "scene": "1005",
                "xweb_xhr": "1",
                "user-agent": ua,
                "tenant-id": "1718857849685876737",
                "accept": "*/*",
                "referer": "https://servicewechat.com/wxf32616183fb4511e/650/page-frame.html",
                "accept-encoding": "gzip, deflate, br",
                "accept-language": "zh-CN,zh;q=0.9"
            }
        }
        $.get(options, async (err, resp, data) => {
            try {
                if (err) {
                    await $.wait(3000)
                    throw new Error('没有响应');
                }
                await $.wait(2000)
                resolve(JSON.parse(data));
            } catch (e) {
                console.log(`${url} API请求失败，剩余重试次数${count - 1}`)
                commonGet(num, url, count - 1).then(resolve).catch(reject);
            }
        })
    })
}

async function commonPost(num, url, body, count = 5) {
    if (count <= 0) {
        throw new Error('请求重试次数超限');
    }
    return new Promise((resolve, reject) => {
        const options = {
            url: `https://msmarket.msx.digitalyili.com${url}`,
            headers: {
                "host": "msmarket.msx.digitalyili.com",
                "register-source": "",
                "access-token": accesstokenArr[num],
                "forward-appid": "",
                "source-type": "",
                "content-type": "application/json",
                "atv-page": "",
                "scene": "1005",
                "xweb_xhr": "1",
                "user-agent": ua,
                "tenant-id": "1718857849685876737",
                "accept": "*/*",
                "referer": "https://servicewechat.com/wxf32616183fb4511e/650/page-frame.html",
                "accept-encoding": "gzip, deflate, br",
                "accept-language": "zh-CN,zh;q=0.9"
            },
            body: JSON.stringify(body)
        }
        $.post(options, async (err, resp, data) => {
            try {
                if (err) {
                    await $.wait(3000)
                    throw new Error('没有响应');
                }
                await $.wait(2000)
                resolve(JSON.parse(data));
            } catch (e) {
                console.log(`${url} API请求失败，剩余重试次数${count - 1}`)
                commonPost(num, url, body, count - 1).then(resolve).catch(reject);
            }
        })
    })
}

async function acPost(num, url, body, count = 5) {
    if (count <= 0) {
        throw new Error('请求重试次数超限');
    }
    return new Promise((resolve, reject) => {
        const options = {
            url: `https://wx-camp-hc-api-02.mscampapi.digitalyili.com${url}`,
            headers: {
                "host": "wx-camp-hc-api-02.mscampapi.digitalyili.com",
                "xweb_xhr": "1",
                "authorization": tokenArr[num],
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090b13) XWEB/9129",
                "content-type": "application/json",
                "accept": "*/*",
                "referer": "https://servicewechat.com/wxf32616183fb4511e/709/page-frame.html",
                "accept-encoding": "gzip, deflate, br",
                "accept-language": "zh-CN,zh;q=0.9"
            },
            body: JSON.stringify(body)
        }
        $.post(options, async (err, resp, data) => {
            try {
                if (err) {
                    await $.wait(3000)
                    throw new Error('没有响应');
                }
                await $.wait(2000)
                resolve(JSON.parse(data));
            } catch (e) {
                console.log(`${url} API请求失败，剩余重试次数${count - 1}`)
                acPost(num, url, body, count - 1).then(resolve).catch(reject);
            }
        })
    })
}

async function getWxCode(num) {
    if (!PHONECODE_SERVER) {
        throw new Error('未配置 PHONECODE_SERVER 环境变量');
    }
    
    try {
        const options = {
            url: `${PHONECODE_SERVER}/api/Wxapp/JSLogin`,
            headers: {
                'accept': 'application/json',
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                'appid': appid,
                'wxid': wxidArr[num]
            })
        }
        
        return new Promise(resolve => {
            $.post(options, async (err, resp, data) => {
                try {
                    if (err) {
                        console.log(`${JSON.stringify(err)}`)
                        console.log(`${$.name} API请求失败，请检查网路重试`)
                        resolve({Code: -1, Message: '请求失败'});
                    } else {
                        const result = JSON.parse(data);
                        if (result && result.Success && result.Data && result.Data.code) {
                            print(num, `获取微信code成功: ${result.Data.code.substring(0, 10)}...`);
                            resolve({Code: 0, Message: '成功', Data: result.Data});
                        } else {
                            resolve({Code: -1, Message: `获取Code失败: ${JSON.stringify(result)}`});
                        }
                    }
                } catch (e) {
                    $.logErr(e, resp)
                    resolve({Code: -1, Message: `请求异常: ${e.message}`});
                }
            })
        })
    } catch (error) {
        return {Code: -1, Message: `账号 ${wxidArr[num]} 获取Code异常: ${error.message}`};
    }
}

function print(num, msg, is_notice = false) {
    let str = `${headArr[num]}${msg}`
    console.log(str)
    if (is_notice) {
        notice += `${str}\n`
    }
}

async function sendMsg(message) {
    if ($.isNode()) {
        let notify = ''
        try {
            notify = require('./sendNotify');
        } catch (e) {
            notify = require("../sendNotify");
        }
        await notify.sendNotify($.name, message);
    } else {
        $.error($.name, '', message)
    }
}